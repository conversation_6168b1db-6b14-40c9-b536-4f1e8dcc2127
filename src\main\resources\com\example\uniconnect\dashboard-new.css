/* Global Styles - Modern DBMS matching design */
.root {
    -fx-font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #F5F7FA;
}

/* Sidebar Styles - Pure black with modern styling */
.sidebar {
    -fx-background-color: #000000;
    -fx-pref-width: 200px;
    -fx-spacing: 0;
    -fx-padding: 25 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 2, 0);
}

.logo-section {
    -fx-padding: 0 25 35 25;
}

.logo-text {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-letter-spacing: 0.5px;
}

.menu-items {
    -fx-padding: 0 0 25 0;
}

.menu-settings {
    -fx-padding: 10 0 0 0;
}

.sidebar-item {
    -fx-padding: 15 25;
    -fx-spacing: 15;
    -fx-alignment: center-left;
    -fx-cursor: hand;
    -fx-transition: all 0.2s ease;
}

.sidebar-item:hover {
    -fx-background-color: rgba(255, 255, 255, 0.12);
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.1), 2, 0, 0, 0);
}

.sidebar-item.active {
    -fx-background-color: rgba(255, 255, 255, 0.18);
    -fx-border-color: #FFFFFF;
    -fx-border-width: 0 0 0 3;
}

.sidebar-icon {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 16px;
    -fx-min-width: 22px;
}

.sidebar-text {
    -fx-text-fill: #FFFFFF;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
}

/* Header Styles - Modern clean design */
.header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 18 25;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 2);
    -fx-border-color: #E8EAED;
    -fx-border-width: 0 0 1 0;
}

.greeting {
    -fx-font-weight: 500;
    -fx-font-size: 17px;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.notification {
    -fx-spacing: 18;
    -fx-alignment: center-right;
}

.icon-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 18px;
    -fx-cursor: hand;
    -fx-padding: 10;
    -fx-background-radius: 8;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
}

.icon-button:hover {
    -fx-background-color: #F1F3F4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.profile-img {
    -fx-background-radius: 50%;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 4, 0, 0, 2);
}

/* Main Content Styles - Modern layout */
.main-content {
    -fx-background-color: transparent;
    -fx-spacing: 25;
}

/* Post Input Area Styles - Enhanced modern design */
.post-input-container {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-border-color: #E8EAED;
    -fx-border-width: 1;
    -fx-border-radius: 12;
}

.post-input-area {
    -fx-alignment: center-left;
}

.post-textarea {
    -fx-pref-height: 45;
    -fx-background-color: #F8F9FA;
    -fx-border-color: #E8EAED;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #5F6368;
}

.post-textarea:focused {
    -fx-border-color: #1877F2;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.2), 4, 0, 0, 1);
}

.post-actions {
    -fx-alignment: center-left;
}

.photo-button {
    -fx-background-color: #F1F3F4;
    -fx-text-fill: #5F6368;
    -fx-padding: 12 16;
    -fx-background-radius: 8;
    -fx-border-color: #DADCE0;
    -fx-border-radius: 8;
    -fx-border-width: 1;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.photo-button:hover {
    -fx-background-color: #E8EAED;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.post-button {
    -fx-background-color: #1877F2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 12 20;
    -fx-background-radius: 8;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.3), 4, 0, 0, 2);
}

.post-button:hover {
    -fx-background-color: #166FE5;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.4), 6, 0, 0, 3);
}

.category-dropdown {
    -fx-background-color: #F8F9FA;
    -fx-background-radius: 8;
    -fx-border-color: #DADCE0;
    -fx-border-radius: 8;
    -fx-border-width: 1;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-cursor: hand;
}

.category-dropdown:focused {
    -fx-border-color: #1877F2;
    -fx-border-width: 2;
}

.profile-circle {
    -fx-fill: #E1E3E6;
    -fx-stroke: #DADCE0;
    -fx-stroke-width: 1.5;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

/* Post Styles - Modern card design */
.section-title {
    -fx-font-weight: 600;
    -fx-font-size: 20px;
    -fx-text-fill: #202124;
    -fx-padding: 0 0 15 0;
    -fx-letter-spacing: 0.3px;
}

.post {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-border-color: #E8EAED;
    -fx-border-width: 1;
    -fx-border-radius: 12;
}

.username {
    -fx-font-weight: 600;
    -fx-font-size: 15px;
    -fx-text-fill: #202124;
}

.timestamp {
    -fx-text-fill: #5F6368;
    -fx-font-size: 13px;
    -fx-font-weight: 400;
}

.post-text {
    -fx-text-fill: #3C4043;
    -fx-font-size: 15px;
    -fx-line-spacing: 3;
    -fx-font-weight: 400;
}

.post-buttons {
    -fx-alignment: center-left;
}

/* Enhanced Like/Comment buttons with state management */
.like-button {
    -fx-background-color: #1877F2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 10 18;
    -fx-background-radius: 8;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.25), 3, 0, 0, 2);
}

.like-button:hover {
    -fx-background-color: #166FE5;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.35), 4, 0, 0, 2);
}

/* Liked state - black background */
.like-button.liked {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 3, 0, 0, 2);
}

.like-button.liked:hover {
    -fx-background-color: #333333;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 4, 0, 0, 2);
}

.comment-button {
    -fx-background-color: #F1F3F4;
    -fx-text-fill: #3C4043;
    -fx-padding: 10 18;
    -fx-background-radius: 8;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-border-color: #DADCE0;
    -fx-border-width: 1;
    -fx-border-radius: 8;
}

.comment-button:hover {
    -fx-background-color: #E8EAED;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.category-tag {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 6;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.5px;
}

/* Right Sidebar Styles - Enhanced modern design */
.right-sidebar {
    -fx-background-color: transparent;
}

.calendar-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-border-color: #E8EAED;
    -fx-border-width: 1;
    -fx-border-radius: 12;
}

.date-label {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.calendar-header {
    -fx-font-size: 15px;
    -fx-font-weight: 500;
    -fx-text-fill: #3C4043;
}

.calendar-status {
    -fx-font-size: 15px;
    -fx-text-fill: #1877F2;
    -fx-font-weight: 500;
}

.routine-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-border-color: #E8EAED;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-max-height: 600;
}

.routine-title {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.class-container {
    -fx-background-color: transparent;
    -fx-max-height: 500;
}

.class-item {
    -fx-background-color: #F8F9FA;
    -fx-padding: 18;
    -fx-background-radius: 10;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1;
    -fx-border-radius: 10;
    -fx-spacing: 10;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.class-item:hover {
    -fx-background-color: #F1F3F4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.class-subject {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
}

.class-details {
    -fx-alignment: center-left;
}

.time-icon {
    -fx-font-size: 14px;
    -fx-text-fill: #5F6368;
}

.class-time {
    -fx-font-size: 14px;
    -fx-text-fill: #5F6368;
    -fx-font-weight: 500;
}

.class-info {
    -fx-alignment: center-left;
}

.section-tag {
    -fx-background-color: #E8F5E8;
    -fx-text-fill: #2E7D32;
    -fx-padding: 6 10;
    -fx-background-radius: 6;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

.room-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 10;
    -fx-background-radius: 6;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}
